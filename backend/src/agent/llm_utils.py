"""LLM工具模块，用于初始化和管理不同的语言模型"""

from math import pi
import os
import re
import json
from typing import Optional, Dict, Any, Type, TypeVar
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.runnables import RunnableLambda
from pydantic import BaseModel
from dotenv import load_dotenv

load_dotenv()

T = TypeVar('T', bound=BaseModel)


def fix_truncated_json(content: str) -> str:
    """
    修复截断的JSON内容

    Args:
        content: 可能被截断的JSON内容

    Returns:
        修复后的JSON字符串
    """
    # 检查是否是截断的JSON
    if not (content.rstrip().endswith('...') or content.count('{') > content.count('}')):
        return content

    print("检测到截断的JSON，开始修复...")

    # 移除截断标记
    fixed_content = re.sub(r'\.\.\..*$', '', content, flags=re.DOTALL).strip()

    # 查找最后一个完整的字段
    lines = fixed_content.split('\n')
    last_complete_line = -1

    for i in range(len(lines) - 1, -1, -1):
        line = lines[i].strip()
        if line and (line.endswith(',') or line.endswith('"') or line.endswith('}') or line.endswith(']')):
            last_complete_line = i
            break

    if last_complete_line >= 0:
        # 保留到最后一个完整行
        lines = lines[:last_complete_line + 1]

        # 移除最后一行的尾随逗号（如果有）
        if lines and lines[-1].strip().endswith(','):
            lines[-1] = lines[-1].rstrip().rstrip(',')

    fixed_content = '\n'.join(lines)

    # 确保JSON对象正确闭合
    open_braces = fixed_content.count('{')
    close_braces = fixed_content.count('}')
    if open_braces > close_braces:
        fixed_content += '\n' + '}' * (open_braces - close_braces)

    # 检查是否缺少必需的字段
    if '"processed_story"' in fixed_content:
        # 确保有所有必需的字段
        required_fields = ['processed_story_en', 'story_type', 'rationale']
        missing_fields = []

        for field in required_fields:
            if f'"{field}"' not in fixed_content:
                missing_fields.append(field)

        if missing_fields:
            # 在最后一个}之前添加缺失的字段
            insert_pos = fixed_content.rfind('}')
            if insert_pos > 0:
                # 检查是否需要添加逗号
                before_brace = fixed_content[:insert_pos].rstrip()
                if before_brace and not before_brace.endswith(','):
                    comma = ','
                else:
                    comma = ''

                # 添加缺失字段
                missing_json = comma + '\n'
                for field in missing_fields:
                    if field == 'processed_story_en':
                        missing_json += f'  "{field}": "",\n'
                    elif field == 'story_type':
                        missing_json += f'  "{field}": "expansion",\n'
                    elif field == 'rationale':
                        missing_json += f'  "{field}": "Story processing completed"\n'

                # 移除最后一个逗号
                missing_json = missing_json.rstrip().rstrip(',') + '\n'

                fixed_content = fixed_content[:insert_pos] + missing_json + fixed_content[insert_pos:]

    return fixed_content


def clean_json_response(response_content: str) -> str:
    """
    清理LLM返回的JSON响应，移除markdown代码块包装并处理控制字符

    Args:
        response_content: LLM返回的原始内容

    Returns:
        清理后的JSON字符串
    """
    if isinstance(response_content, str):
        # 移除markdown代码块包装
        content = response_content.strip()

        # 处理 ```json...``` 包装
        if content.startswith('```json') and content.endswith('```'):
            content = content[7:-3].strip()  # 移除 ```json 和 ```
        elif content.startswith('```') and content.endswith('```'):
            content = content[3:-3].strip()  # 移除 ```

        # 处理其他可能的包装格式
        content = re.sub(r'^```\w*\n', '', content)  # 移除开头的代码块标记
        content = re.sub(r'\n```$', '', content)    # 移除结尾的代码块标记

        # 清理控制字符但保留必要的换行和制表符
        # 移除非打印控制字符，但保留换行符(\n)、回车符(\r)、制表符(\t)
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)

        # 标准化换行符
        content = content.replace('\r\n', '\n').replace('\r', '\n')

        # 查找JSON对象的开始和结束位置
        # 寻找第一个 { 和最后一个 }
        start_idx = content.find('{')
        end_idx = content.rfind('}')

        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            # 提取JSON对象部分
            content = content[start_idx:end_idx + 1]

        # 尝试修复截断的JSON
        content = fix_truncated_json(content)

        # 在JSON字符串值中转义控制字符
        # 这个正则表达式匹配JSON字符串值并转义其中的控制字符
        def escape_json_strings(match):
            string_content = match.group(1)
            # 转义常见的控制字符
            string_content = string_content.replace('\n', '\\n')
            string_content = string_content.replace('\t', '\\t')
            string_content = string_content.replace('\r', '\\r')
            string_content = string_content.replace('\b', '\\b')
            string_content = string_content.replace('\f', '\\f')
            return f'"{string_content}"'

        # 匹配JSON字符串值并转义控制字符
        content = re.sub(r'"([^"\\]*(\\.[^"\\]*)*)"', escape_json_strings, content)

        return content.strip()

    return response_content


def create_structured_output_chain(llm, output_schema: Type[T]):
    """
    创建带有JSON清理功能的结构化输出链
    
    Args:
        llm: 语言模型实例
        output_schema: 输出数据结构的Pydantic模型
        
    Returns:
        配置好的结构化输出链
    """
    def parse_and_clean(llm_output):
        """解析和清理LLM输出"""
        # 获取LLM输出内容
        if hasattr(llm_output, 'content'):
            content = llm_output.content
        else:
            content = str(llm_output)
        
        print(f"\n=== LLM原始输出 ===\n{content[:500]}..." if len(content) > 500 else f"\n=== LLM原始输出 ===\n{content}")
        
        # 清理JSON内容
        cleaned_content = clean_json_response(content)
        
        print(f"\n=== 清理后JSON ===\n{cleaned_content[:500]}..." if len(cleaned_content) > 500 else f"\n=== 清理后JSON ===\n{cleaned_content}")
        
        try:
            # 尝试解析JSON
            json_data = json.loads(cleaned_content)
            print(f"\n=== JSON解析成功 ===\n数据类型: {type(json_data)}")
            
            # 使用Pydantic模型验证和解析
            result = output_schema.model_validate(json_data)
            print(f"\n=== Pydantic验证成功 ===\n结果类型: {type(result)}")
            return result
            
        except json.JSONDecodeError as e:
            print(f"\n=== JSON解析失败 ===\n错误: {e}")
            print(f"错误位置: 第{e.lineno}行第{e.colno}列")
            
            # 尝试显示错误附近的内容
            lines = cleaned_content.split('\n')
            if e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                print(f"错误行内容: {error_line}")
                if e.colno <= len(error_line):
                    pointer = ' ' * (e.colno - 1) + '^'
                    print(f"错误位置: {pointer}")
                    
                    # 显示错误字符的ASCII值
                    if e.colno - 1 < len(error_line):
                        error_char = error_line[e.colno - 1]
                        print(f"错误字符: '{error_char}' (ASCII: {ord(error_char)})")
            
            # 尝试修复常见问题
            try:
                print("\n=== 尝试修复 ===\n")

                # 方法1: 移除所有控制字符（除了空格、换行符和制表符）
                fixed_content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]', '', cleaned_content)
                json_data = json.loads(fixed_content)
                print(f"修复方法1成功: 移除控制字符")
                return output_schema.model_validate(json_data)

            except:
                try:
                    # 方法2: 更激进的清理，只保留可打印字符
                    import string
                    printable_chars = set(string.printable)
                    fixed_content = ''.join(c for c in cleaned_content if c in printable_chars)
                    json_data = json.loads(fixed_content)
                    print(f"修复方法2成功: 只保留可打印字符")
                    return output_schema.model_validate(json_data)

                except:
                    try:
                        # 方法3: 重新查找JSON对象边界
                        print("尝试方法3: 重新查找JSON对象边界")
                        start_idx = cleaned_content.find('{')
                        end_idx = cleaned_content.rfind('}')

                        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
                            json_part = cleaned_content[start_idx:end_idx + 1]
                            # 移除可能的前导/尾随空白和换行
                            json_part = json_part.strip()
                            json_data = json.loads(json_part)
                            print(f"修复方法3成功: 重新提取JSON对象")
                            return output_schema.model_validate(json_data)
                        else:
                            raise ValueError("无法找到有效的JSON对象边界")

                    except:
                        try:
                            # 方法4: 尝试修复常见的JSON格式错误
                            print("尝试方法4: 修复常见JSON格式错误")
                            fixed_content = cleaned_content

                            # 修复可能的尾随逗号
                            fixed_content = re.sub(r',(\s*[}\]])', r'\1', fixed_content)

                            # 修复可能的单引号
                            fixed_content = re.sub(r"'([^']*)':", r'"\1":', fixed_content)
                            fixed_content = re.sub(r":\s*'([^']*)'", r': "\1"', fixed_content)

                            json_data = json.loads(fixed_content)
                            print(f"修复方法4成功: 修复JSON格式错误")
                            return output_schema.model_validate(json_data)

                        except:
                            try:
                                # 方法5: 使用专门的截断JSON修复函数
                                print("尝试方法5: 使用专门的截断JSON修复函数")
                                fixed_content = fix_truncated_json(cleaned_content)

                                json_data = json.loads(fixed_content)
                                print(f"修复方法5成功: 截断JSON修复")
                                return output_schema.model_validate(json_data)

                            except:
                                print(f"所有修复尝试都失败")
                                print(f"最终内容预览: {cleaned_content[:200]}...")
                                raise e
                    
        except Exception as e:
            print(f"\n=== Pydantic验证失败 ===\n错误: {e}")
            raise e
    
    # 创建处理链：LLM -> 清理和解析
    return llm | RunnableLambda(parse_and_clean)


def get_qwen_llm(
    model_name: str = "qwen-max-0403",
    temperature: float = 0.7,
    max_retries: int = 2,
    **kwargs
) -> ChatOpenAI:
    """
    初始化Qwen模型（通过OpenAI兼容的API）
    
    Args:
        model_name: 模型名称
        temperature: 温度参数
        max_retries: 最大重试次数
        **kwargs: 其他参数
    
    Returns:
        ChatOpenAI: 配置好的Qwen模型实例
    """
    # 从环境变量获取配置
    api_key = os.getenv("QWEN_API_KEY") or os.getenv("DASHSCOPE_API_KEY", "your-qwen-api-key")
    base_url = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")

    print(f"Using Qwen model: {model_name}") 
    print(f"Using Qwen API key: {api_key}")
    print(f"Using Qwen base URL: {base_url}")
       

    return ChatOpenAI(
        model=model_name,
        api_key=api_key,
        base_url=base_url,
        temperature=temperature,
        max_retries=max_retries,
        **kwargs
    )


def get_gemini_llm(
    model_name: str = "gemini-2.0-flash",
    temperature: float = 0.7,
    max_retries: int = 2,
    **kwargs
) -> ChatGoogleGenerativeAI:
    """
    初始化Gemini模型（保留兼容性）
    
    Args:
        model_name: 模型名称
        temperature: 温度参数
        max_retries: 最大重试次数
        **kwargs: 其他参数
    
    Returns:
        ChatGoogleGenerativeAI: 配置好的Gemini模型实例
    """
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY is not set")
    
    return ChatGoogleGenerativeAI(
        model=model_name,
        api_key=api_key,
        temperature=temperature,
        max_retries=max_retries,
        **kwargs
    )


def get_llm_by_provider(
    provider: str,
    model_name: str,
    temperature: float = 0.7,
    max_retries: int = 2,
    **kwargs
):
    """
    根据提供商获取对应的LLM实例
    
    Args:
        provider: 提供商名称 (qwen/gemini)
        model_name: 模型名称
        temperature: 温度参数
        max_retries: 最大重试次数
        **kwargs: 其他参数
    
    Returns:
        对应的LLM实例
    """
    if provider.lower() == "qwen":
        return get_qwen_llm(model_name, temperature, max_retries, **kwargs)
    elif provider.lower() == "gemini":
        return get_gemini_llm(model_name, temperature, max_retries, **kwargs)
    else:
        raise ValueError(f"Unsupported provider: {provider}")


def get_default_llm(
    model_name: str = "qwen-max-0403",
    temperature: float = 0.7,
    max_retries: int = 2,
    **kwargs
):
    """
    获取默认的LLM实例（Qwen）
    
    Args:
        model_name: 模型名称
        temperature: 温度参数
        max_retries: 最大重试次数
        **kwargs: 其他参数
    
    Returns:
        默认的LLM实例
    """
    return get_qwen_llm(model_name, temperature, max_retries, **kwargs)
